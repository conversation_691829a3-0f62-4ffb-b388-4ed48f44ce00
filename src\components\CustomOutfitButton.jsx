import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Shirt, Plus, Check, ArrowRight, ChevronDown, X, Save } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useOutfit } from '../context/OutfitContext';
import { useAuth } from '../context/AuthContext';
import { dataService } from '../services/dataService';

const CustomOutfitButton = ({ product, selectedColor, selectedSize }) => {
  const navigate = useNavigate();
  const { isAuthenticated, user } = useAuth();
  const {
    savedOutfits,
    setSavedOutfits,
    saveOutfit,
    updateOutfit,
    currentOutfit,
    addToCurrentOutfit,
    removeFromCurrentOutfit,
    clearCurrentOutfit,
    currentOutfitCount
  } = useOutfit();

  const [showDropdown, setShowDropdown] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showOutfitModal, setShowOutfitModal] = useState(false);
  const [selectedOutfitId, setSelectedOutfitId] = useState(null);
  const [newOutfitName, setNewOutfitName] = useState('');
  const [showSuccess, setShowSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  // Check if product is in any saved outfit
  const isInSavedOutfit = (outfitId) => {
    const outfit = savedOutfits.find(o => o.id === outfitId);
    return outfit?.items.some(item => item.id === product.id);
  };

  // Check if product is in current outfit
  const isInCurrentOutfit = currentOutfit.some(item => item.id === product.id);

  // Add or remove product from a specific saved outfit
  const toggleProductInOutfit = async (outfitId) => {
    const outfit = savedOutfits.find(o => o.id === outfitId);
    if (!outfit) return;

    try {
      if (isInSavedOutfit(outfitId)) {
        // Remove product from outfit - find the item to remove
        const itemToRemove = outfit.items.find(item => item.id === product.id);
        if (itemToRemove && itemToRemove.outfit_item_id) {
          // Call API to remove item
          await dataService.removeItemFromOutfit(user?.id, outfitId, itemToRemove.outfit_item_id);
          setSuccessMessage(`Removed from "${outfit.name}"`);
        } else {
          // Fallback to local update if no outfit_item_id
          const updatedItems = outfit.items.filter(item => item.id !== product.id);
          updateOutfit(outfitId, {
            items: updatedItems,
            totalPrice: updatedItems.reduce((total, item) => total + (item.salePrice || item.price), 0),
            total_price: updatedItems.reduce((total, item) => total + (item.price || 0), 0),
            total_sale_price: updatedItems.reduce((total, item) => total + (item.salePrice || item.price || 0), 0)
          });
          setSuccessMessage(`Removed from "${outfit.name}"`);
        }
      } else {
        // Add product to outfit
        const colorObj = selectedColor || product.colors?.[0] || null;
        const colorName = typeof colorObj === 'object' ? colorObj.name : colorObj || 'Default';
        const colorHex = typeof colorObj === 'object' ? colorObj.hex : null;

        const itemData = {
          product_id: product.id,
          selected_color: colorName,
          selected_size: selectedSize || product.sizes?.[0] || 'M',
          selected_color_hex: colorHex,
          category_type: getCategoryType(product.category),
          is_primary: false
        };

        console.log('🎨 CUSTOM OUTFIT - Adding to existing outfit:', {
          outfitName: outfit.name,
          productId: product.id,
          productName: product.name,
          selectedColor: colorName,
          selectedSize: selectedSize || product.sizes?.[0] || 'M',
          colorHex: colorHex,
          price: product.price,
          salePrice: product.salePrice
        });

        // Call API to add item to outfit
        if (isAuthenticated && user?.id) {
          await dataService.addItemToOutfit(user.id, outfitId, itemData);
        } else {
          // Fallback to local update
          const updatedItems = [...outfit.items, {
            ...product,
            selectedColor: colorName,
            selectedSize: selectedSize || product.sizes?.[0] || 'M',
            colorHex: colorHex,
            price: product.price,
            salePrice: product.salePrice
          }];

          updateOutfit(outfitId, {
            items: updatedItems,
            totalPrice: updatedItems.reduce((total, item) => total + (item.salePrice || item.price), 0),
            total_price: updatedItems.reduce((total, item) => total + (item.price || 0), 0),
            total_sale_price: updatedItems.reduce((total, item) => total + (item.salePrice || item.price || 0), 0)
          });
        }
        setSuccessMessage(`Added to "${outfit.name}"`);
      }

      // Refresh outfits to get updated data
      if (isAuthenticated && user?.id) {
        const updatedOutfits = await dataService.getOutfits(user.id);
        setSavedOutfits(updatedOutfits);
      }

    } catch (error) {
      console.error('Failed to toggle product in outfit:', error);
      setSuccessMessage('Failed to update outfit');
    }

    setShowSuccess(true);
    setTimeout(() => setShowSuccess(false), 2000);
    setShowDropdown(false);
  };

  // Navigate to outfits page
  const navigateToOutfits = () => {
    setShowDropdown(false);
    navigate('/outfits');
  };

  // Create new outfit with current product
  const createNewOutfit = () => {
    if (newOutfitName.trim()) {
      // Get the selected color object or default to first color
      const colorObj = selectedColor || product.colors?.[0] || null;
      const colorName = typeof colorObj === 'object' ? colorObj.name : colorObj || 'Default';
      const colorHex = typeof colorObj === 'object' ? colorObj.hex : null;

      const outfitItem = {
        ...product,
        selectedColor: colorName, // Store color name as string
        selectedSize: selectedSize || product.sizes?.[0] || 'M',
        colorHex: colorHex, // Store hex separately if available
        // Ensure we have the correct price
        price: product.price,
        salePrice: product.salePrice
      };

      console.log('🎨 CUSTOM OUTFIT - Creating outfit with item:', {
        productId: product.id,
        productName: product.name,
        selectedColor: colorName,
        selectedSize: outfitItem.selectedSize,
        colorHex: colorHex,
        price: product.price,
        salePrice: product.salePrice
      });

      saveOutfit([outfitItem], newOutfitName.trim());
      setNewOutfitName('');
      setShowCreateModal(false);
      setShowDropdown(false);
      setSuccessMessage(`Created "${newOutfitName.trim()}" outfit`);
      setShowSuccess(true);
      setTimeout(() => setShowSuccess(false), 2000);
    }
  };

  // Add to current outfit (temporary)
  const handleAddToCurrentOutfit = () => {
    if (!isInCurrentOutfit) {
      addToCurrentOutfit({
        ...product,
        selectedColor: selectedColor || product.colors?.[0] || null,
        selectedSize: selectedSize || product.sizes?.[0] || 'M'
      });
    }
    setShowDropdown(false);
  };

  // Save current outfit
  const saveCurrentOutfit = () => {
    if (currentOutfitCount > 0) {
      const outfitName = `Outfit ${savedOutfits.length + 1}`;
      saveOutfit(currentOutfit, outfitName);
      clearCurrentOutfit();
      setSuccessMessage(`Saved "${outfitName}"`);
      setShowSuccess(true);
      setTimeout(() => setShowSuccess(false), 2000);
    }
  };

  return (
    <div className="space-y-4 relative">
      {/* Add to Custom Outfit Button with Dropdown */}
      <div className="relative">
        <motion.button
          onClick={() => setShowDropdown(!showDropdown)}
          className="w-full py-3 px-6 rounded-xl font-medium transition-all duration-300 flex items-center justify-center gap-3 text-white hover:shadow-lg hover:scale-[1.02]"
          style={{
            backgroundColor: 'rgba(74, 222, 128, 0.2)', // #4ade80 with transparency
            border: '1px solid rgba(74, 222, 128, 0.3)',
            boxShadow: '0 0 20px rgba(74, 222, 128, 0.15)'
          }}
          onMouseEnter={(e) => {
            e.target.style.backgroundColor = 'rgba(74, 222, 128, 0.3)';
            e.target.style.boxShadow = '0 0 25px rgba(74, 222, 128, 0.25)';
          }}
          onMouseLeave={(e) => {
            e.target.style.backgroundColor = 'rgba(74, 222, 128, 0.2)';
            e.target.style.boxShadow = '0 0 20px rgba(74, 222, 128, 0.15)';
          }}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          {/* <Plus size={20} style={{ color: '#4ade80' }} /> */}
          <span style={{ color: '#4ade80' }}>Add to Custom Outfit</span>
          <ChevronDown size={16} className={`transition-transform ${showDropdown ? 'rotate-180' : ''}`} style={{ color: '#4ade80' }} />
        </motion.button>

        {/* Dropdown Menu */}
        <AnimatePresence>
          {showDropdown && (
            <>
              {/* Backdrop */}
              <div
                className="fixed inset-0 z-40"
                onClick={() => setShowDropdown(false)}
              />

              {/* Dropdown Content */}
              <motion.div
                initial={{ opacity: 0, y: -10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -10, scale: 0.95 }}
                className="absolute top-full left-0 right-0 mt-2 bg-[#1a1a1a] backdrop-blur-xl border border-[#404040] rounded-xl shadow-xl z-50 overflow-hidden"
              >
                {/* Current Outfit Section */}
                {currentOutfitCount > 0 && (
                  <div className="p-4 border-b border-[#2a2a2a]">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="text-white font-medium text-sm">Current Outfit ({currentOutfitCount} items)</h4>
                      <button
                        onClick={saveCurrentOutfit}
                        className="text-green-400 hover:text-green-300 text-sm font-medium flex items-center gap-1"
                      >
                        <Save size={14} />
                        Save
                      </button>
                    </div>

                    <div className="space-y-2 max-h-32 overflow-y-auto">
                      {currentOutfit.map((item, index) => (
                        <div key={item.id} className="flex items-center gap-2 bg-[#2a2a2a] rounded-lg p-2">
                          <img
                            src={item.selectedColor?.images?.[0] || item.images[0]}
                            alt={item.name}
                            className="w-8 h-8 object-cover rounded"
                          />
                          <span className="text-white text-xs flex-1 truncate">{item.name}</span>
                          <button
                            onClick={() => removeFromCurrentOutfit(item.id)}
                            className="text-red-400 hover:text-red-300"
                          >
                            <X size={14} />
                          </button>
                        </div>
                      ))}
                    </div>

                    <button
                      onClick={handleAddToCurrentOutfit}
                      disabled={isInCurrentOutfit}
                      className={`w-full mt-3 py-2 px-3 rounded-lg text-sm font-medium transition-all ${
                        isInCurrentOutfit
                          ? 'bg-green-600/20 text-green-400 cursor-not-allowed'
                          : 'bg-blue-700 hover:bg-blue-600 text-white'
                      }`}
                    >
                      {isInCurrentOutfit ? 'Already in Current Outfit' : 'Add to Current Outfit'}
                    </button>
                  </div>
                )}

                {/* Saved Outfits Section */}
                <div className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-white font-medium text-sm">Add to Saved Outfit</h4>
                    {/* View All Outfits - Top Right */}
                    <button
                      onClick={navigateToOutfits}
                      className="text-blue-400 hover:text-blue-300 text-xs font-medium transition-colors flex items-center gap-1 hover:underline"
                    >
                      View All
                      <ArrowRight size={10} />
                    </button>
                  </div>

                  {savedOutfits.length > 0 ? (
                    <div className="space-y-2 max-h-40 overflow-y-auto mb-3">
                      {savedOutfits.map((outfit) => (
                        <button
                          key={outfit.id}
                          onClick={() => toggleProductInOutfit(outfit.id)}
                          className={`w-full text-left p-3 rounded-lg transition-all ${
                            isInSavedOutfit(outfit.id)
                              ? 'bg-green-600/20 text-green-300 border border-green-500/30 hover:bg-green-600/30'
                              : 'bg-[#2a2a2a] hover:bg-[#404040] text-white'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <div>
                              <div className="font-medium text-sm">{outfit.name}</div>
                              <div className={`text-xs ${isInSavedOutfit(outfit.id) ? 'text-green-400' : 'text-[#AAAAAA]'}`}>
                                {outfit.items.length} items • ${(outfit.total_sale_price || outfit.total_price || outfit.totalPrice || 0).toFixed(2)}
                              </div>
                            </div>
                            {isInSavedOutfit(outfit.id) ? (
                              <div className="flex items-center gap-1">
                                <Check size={16} className="text-green-400" />
                                <span className="text-xs text-green-400">Added</span>
                              </div>
                            ) : (
                              <Plus size={16} className="text-slate-400" />
                            )}
                          </div>
                        </button>
                      ))}
                    </div>
                  ) : (
                    <p className="text-[#AAAAAA] text-sm mb-3">No saved outfits yet</p>
                  )}



                  {/* Create New Outfit Button */}
                  <button
                    onClick={() => {
                      setShowCreateModal(true);
                      setShowDropdown(false);
                    }}
                    className="w-full py-2 px-3 text-white rounded-lg font-medium text-sm hover:shadow-lg hover:shadow-blue-500/25 transition-all flex items-center justify-center gap-2"
                    style={{
                      background: 'linear-gradient(to bottom right, #FF6B35 0%, #F7931E 100%)'
                    }}
                  >
                    <Plus size={14} />
                    Create New Outfit
                  </button>
                </div>
              </motion.div>
            </>
          )}
        </AnimatePresence>
      </div>

      {/* Create New Outfit Modal */}
      <AnimatePresence>
        {showCreateModal && (
          <>
            <div className="fixed inset-0 flex items-center justify-center p-3 z-50">
              {/* Backdrop */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="absolute inset-0 bg-black/60 backdrop-blur-sm"
                onClick={() => setShowCreateModal(false)}
              />

              {/* Modal Content */}
              <motion.div
                initial={{ opacity: 0, scale: 0.9, y: 20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.9, y: 20 }}
                className="relative w-full max-w-sm bg-[#1a1a1a] backdrop-blur-xl border border-[#404040] rounded-xl p-4"
                onClick={(e) => e.stopPropagation()}
              >
                <h3 className="text-white font-semibold text-lg mb-3 flex items-center gap-2">
                  <Shirt size={16} className="text-green-400" />
                  Create New Outfit
                </h3>

                <div className="mb-4">
                  <label className="text-[#AAAAAA] text-sm font-medium mb-2 block">Outfit Name</label>
                  <input
                    type="text"
                    placeholder="Enter outfit name..."
                    value={newOutfitName}
                    onChange={(e) => setNewOutfitName(e.target.value)}
                    className="w-full bg-[#2a2a2a] border border-[#404040] rounded-lg px-3 py-2 text-white placeholder-[#AAAAAA] focus:outline-none focus:border-green-500/50 text-sm"
                    autoFocus
                    onKeyPress={(e) => e.key === 'Enter' && createNewOutfit()}
                  />
                </div>

                <div className="flex gap-2">
                  <button
                    onClick={() => setShowCreateModal(false)}
                    className="flex-1 bg-[#404040] hover:bg-[#6a6a6a] text-white py-2 px-3 rounded-lg font-medium transition-all duration-300 text-sm"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={createNewOutfit}
                    disabled={!newOutfitName.trim()}
                    className="flex-1 text-white py-2 px-3 rounded-lg font-medium hover:shadow-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                    style={{
                      background: !newOutfitName.trim() ? '#64748b' : 'linear-gradient(135deg, #10b981 0%, #059669 100%)'
                    }}
                  >
                    Create Outfit
                  </button>
                </div>
              </motion.div>
            </div>
          </>
        )}
      </AnimatePresence>

      {/* Success Message */}
      <AnimatePresence>
        {showSuccess && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 50 }}
            className="fixed bottom-6 right-6 bg-green-600 text-white px-6 py-3 rounded-lg shadow-lg z-50 flex items-center gap-2"
          >
            <Check size={20} />
            {successMessage || 'Action completed successfully!'}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default CustomOutfitButton;
